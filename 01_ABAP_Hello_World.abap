*&---------------------------------------------------------------------*
*& Report  Z_HELLO_WORLD_DEMO
*&---------------------------------------------------------------------*
*& 程序名称: ABAP 入门示例程序
*& 作者: ABAP 学习者
*& 创建日期: 2025-01-01
*& 描述: 这是一个 ABAP 入门示例程序，展示基础语法和概念
*&---------------------------------------------------------------------*

REPORT z_hello_world_demo.

*&---------------------------------------------------------------------*
*& 数据声明部分 - 这里声明程序中使用的变量
*&---------------------------------------------------------------------*

" 传统的数据声明方式
DATA: lv_message TYPE string VALUE 'Hello ABAP World!',  " 字符串变量
      lv_number  TYPE i VALUE 42,                        " 整数变量
      lv_date    TYPE d VALUE '20250101',                 " 日期变量
      lv_time    TYPE t VALUE '120000'.                   " 时间变量

" 现代的内联声明方式（推荐使用）
DATA(lv_user_name) = sy-uname.                           " 当前用户名
DATA(lv_system_date) = sy-datum.                         " 系统当前日期
DATA(lv_system_time) = sy-uzeit.                         " 系统当前时间

*&---------------------------------------------------------------------*
*& 常量声明 - 程序中不会改变的值
*&---------------------------------------------------------------------*

CONSTANTS: lc_company_name TYPE string VALUE 'SAP学习公司',  " 公司名称常量
           lc_version      TYPE string VALUE 'V1.0'.        " 版本号常量

*&---------------------------------------------------------------------*
*& 主程序开始执行点
*&---------------------------------------------------------------------*

START-OF-SELECTION.

  " 输出欢迎信息
  WRITE: / '=================================================',
         / '🎉 欢迎来到 ABAP 编程世界！',
         / '=================================================',
         /.

  " 显示基本信息
  WRITE: / '📝 程序信息:',
         / '   公司名称: ', lc_company_name,
         / '   程序版本: ', lc_version,
         / '   欢迎消息: ', lv_message,
         /.

  " 显示系统信息
  WRITE: / '🖥️  系统信息:',
         / '   当前用户: ', lv_user_name,
         / '   系统日期: ', lv_system_date,
         / '   系统时间: ', lv_system_time,
         /.

  " 显示数值计算示例
  DATA(lv_result) = lv_number * 2.                        " 内联声明并计算
  WRITE: / '🔢 数值计算示例:',
         / '   原始数值: ', lv_number,
         / '   计算结果: ', lv_number, ' × 2 = ', lv_result,
         /.

  " 条件判断示例
  WRITE: / '🔍 条件判断示例:'.
  
  IF lv_number > 40.
    WRITE: / '   数值', lv_number, '大于 40 ✅'.
  ELSE.
    WRITE: / '   数值', lv_number, '不大于 40 ❌'.
  ENDIF.

  " 循环示例
  WRITE: / '',
         / '🔄 循环示例 (输出 1 到 5):'.
  
  DO 5 TIMES.
    WRITE: / '   循环第', sy-index, '次'.               " sy-index 是系统变量，表示当前循环次数
  ENDDO.

  " 字符串处理示例
  DATA(lv_full_message) = |{ lc_company_name } - { lv_message }|.  " 字符串模板语法
  WRITE: / '',
         / '📝 字符串处理示例:',
         / '   完整消息: ', lv_full_message,
         /.

  " 程序结束信息
  WRITE: / '=================================================',
         / '🎊 程序执行完成！感谢您学习 ABAP！',
         / '================================================='.

*&---------------------------------------------------------------------*
*& 程序说明和学习要点
*&---------------------------------------------------------------------*
*
* 🎯 本程序演示的 ABAP 基础概念：
*
* 1. 📋 程序结构：
*    - 程序头部注释
*    - REPORT 语句
*    - 数据声明部分
*    - 主程序逻辑
*
* 2. 📊 数据类型：
*    - string: 字符串类型
*    - i: 整数类型  
*    - d: 日期类型
*    - t: 时间类型
*
* 3. 🔧 语法特点：
*    - 传统声明 vs 内联声明
*    - 常量声明
*    - 系统变量使用 (sy-*)
*    - 字符串模板 |{ }|
*
* 4. 🎮 控制结构：
*    - IF-ELSE 条件判断
*    - DO-ENDDO 循环
*
* 5. 📤 输出语句：
*    - WRITE 语句
*    - 格式化输出
*
* 💡 学习建议：
*    - 仔细阅读每行注释
*    - 尝试修改变量值观察输出变化
*    - 练习添加新的变量和计算
*
*&---------------------------------------------------------------------*
