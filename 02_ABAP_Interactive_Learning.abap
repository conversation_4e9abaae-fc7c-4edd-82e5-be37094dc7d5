*&---------------------------------------------------------------------*
*& Interactive ABAP Learning Program
*&---------------------------------------------------------------------*
*& 程序名称: ABAP 交互式学习程序
*& 描述: 通过注释和示例代码学习 ABAP 语法
*& 学习方式: 阅读代码 → 理解概念 → 动手练习
*&---------------------------------------------------------------------*

REPORT z_interactive_learning.

*&---------------------------------------------------------------------*
*& 🎯 学习目标：通过这个程序学习 ABAP 基础语法
*&---------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& 📚 第1课：数据声明 (Data Declaration)
*&---------------------------------------------------------------------*

" ✅ 练习1：基本数据类型声明
" 任务：声明不同类型的变量

" 字符串类型 - 用于存储文本
DATA: lv_student_name TYPE string VALUE '张三',        " 学生姓名
      lv_course_name  TYPE string VALUE 'ABAP编程'.    " 课程名称

" 整数类型 - 用于存储数字
DATA: lv_student_age  TYPE i VALUE 25,                 " 学生年龄
      lv_score        TYPE i VALUE 85.                 " 考试分数

" 日期和时间类型
DATA: lv_exam_date    TYPE d VALUE '20250115',         " 考试日期 (YYYYMMDD格式)
      lv_exam_time    TYPE t VALUE '140000'.           " 考试时间 (HHMMSS格式)

" 小数类型
DATA: lv_gpa          TYPE p DECIMALS 2 VALUE '3.75'.  " 平均绩点

" 💡 现代 ABAP 内联声明方式 (推荐)
DATA(lv_university) = '北京大学'.                      " 自动推断为 string 类型
DATA(lv_semester) = 2025.                              " 自动推断为 i 类型

*&---------------------------------------------------------------------*
*& 📚 第2课：常量声明 (Constants Declaration)
*&---------------------------------------------------------------------*

" ✅ 练习2：声明常量
" 任务：声明程序中不会改变的值

CONSTANTS: lc_max_score     TYPE i VALUE 100,          " 最高分数
           lc_pass_score    TYPE i VALUE 60,           " 及格分数
           lc_company_name  TYPE string VALUE 'SAP学习中心',
           lc_version       TYPE string VALUE 'V2.0'.

*&---------------------------------------------------------------------*
*& 📚 第3课：系统变量 (System Variables)
*&---------------------------------------------------------------------*

" ✅ 练习3：使用系统变量
" 任务：了解常用的系统变量

" 常用系统变量说明：
" sy-datum  - 当前系统日期
" sy-uzeit  - 当前系统时间  
" sy-uname  - 当前登录用户
" sy-index  - DO循环中的计数器
" sy-tabix  - 内表处理中的行号
" sy-subrc  - 上一个操作的返回码 (0=成功, 非0=失败)

*&---------------------------------------------------------------------*
*& 📚 第4课：程序主体 (Main Program)
*&---------------------------------------------------------------------*

START-OF-SELECTION.

  " ✅ 练习4：输出语句 (WRITE Statement)
  " 任务：使用 WRITE 语句输出信息

  " 输出标题
  WRITE: / '=======================================================',
         / '🎓 欢迎来到 ABAP 交互式学习程序！',
         / '=======================================================',
         /.

  " 输出学生信息
  WRITE: / '📋 学生信息:',
         / '   姓名: ', lv_student_name,
         / '   年龄: ', lv_student_age,
         / '   大学: ', lv_university,
         / '   学期: ', lv_semester,
         /.

  " 输出课程信息
  WRITE: / '📚 课程信息:',
         / '   课程名称: ', lv_course_name,
         / '   考试日期: ', lv_exam_date,
         / '   考试时间: ', lv_exam_time,
         / '   当前分数: ', lv_score,
         / '   平均绩点: ', lv_gpa,
         /.

  " 输出系统信息
  WRITE: / '🖥️ 系统信息:',
         / '   当前用户: ', sy-uname,
         / '   系统日期: ', sy-datum,
         / '   系统时间: ', sy-uzeit,
         /.

*&---------------------------------------------------------------------*
*& 📚 第5课：条件判断 (IF Statement)
*&---------------------------------------------------------------------*

  " ✅ 练习5：条件判断
  " 任务：根据分数判断成绩等级

  WRITE: / '🔍 成绩评定:'.

  " 简单 IF 语句
  IF lv_score >= lc_pass_score.
    WRITE: / '   ✅ 恭喜！您已通过考试！'.
  ELSE.
    WRITE: / '   ❌ 很遗憾，您需要重新考试。'.
  ENDIF.

  " 多重条件判断
  IF lv_score >= 90.
    WRITE: / '   🌟 成绩等级: 优秀 (A)'.
  ELSEIF lv_score >= 80.
    WRITE: / '   👍 成绩等级: 良好 (B)'.
  ELSEIF lv_score >= 70.
    WRITE: / '   📖 成绩等级: 中等 (C)'.
  ELSEIF lv_score >= 60.
    WRITE: / '   📝 成绩等级: 及格 (D)'.
  ELSE.
    WRITE: / '   📚 成绩等级: 不及格 (F)'.
  ENDIF.

  WRITE: /.

*&---------------------------------------------------------------------*
*& 📚 第6课：CASE 语句 (CASE Statement)
*&---------------------------------------------------------------------*

  " ✅ 练习6：CASE 语句
  " 任务：根据成绩等级给出建议

  DATA(lv_grade) = 'B'.  " 假设成绩等级为 B

  WRITE: / '🎯 学习建议 (基于成绩等级 ', lv_grade, '):'.

  CASE lv_grade.
    WHEN 'A'.
      WRITE: / '   🏆 优秀！继续保持，可以考虑更高级的课程。'.
    WHEN 'B'.
      WRITE: / '   👏 良好！稍加努力就能达到优秀水平。'.
    WHEN 'C'.
      WRITE: / '   💪 中等，需要加强基础知识的学习。'.
    WHEN 'D'.
      WRITE: / '   📖 及格，建议多做练习题巩固知识。'.
    WHEN 'F'.
      WRITE: / '   📚 不及格，建议重新学习基础概念。'.
    WHEN OTHERS.
      WRITE: / '   ❓ 未知等级，请检查输入。'.
  ENDCASE.

  WRITE: /.

*&---------------------------------------------------------------------*
*& 📚 第7课：DO 循环 (DO Loop)
*&---------------------------------------------------------------------*

  " ✅ 练习7：DO 循环
  " 任务：输出倒计时

  WRITE: / '⏰ 考试倒计时:'.

  DO 5 TIMES.
    WRITE: / '   距离考试还有 ', 6 - sy-index, ' 天'.
  ENDDO.

  WRITE: / '   🚀 考试开始！',
         /.

*&---------------------------------------------------------------------*
*& 📚 第8课：WHILE 循环 (WHILE Loop)
*&---------------------------------------------------------------------*

  " ✅ 练习8：WHILE 循环
  " 任务：计算累加和

  DATA: lv_sum     TYPE i VALUE 0,
        lv_counter TYPE i VALUE 1.

  WRITE: / '🔢 计算 1 到 10 的累加和:'.

  WHILE lv_counter <= 10.
    lv_sum = lv_sum + lv_counter.
    WRITE: / '   ', lv_counter, ' + ... = ', lv_sum.
    lv_counter = lv_counter + 1.
  ENDWHILE.

  WRITE: / '   📊 最终结果: 1+2+...+10 = ', lv_sum,
         /.

*&---------------------------------------------------------------------*
*& 📚 第9课：字符串处理 (String Processing)
*&---------------------------------------------------------------------*

  " ✅ 练习9：字符串操作
  " 任务：字符串连接和处理

  WRITE: / '📝 字符串处理示例:'.

  " 字符串连接 - 传统方式
  DATA(lv_full_name) = |{ lv_student_name } 同学|.
  WRITE: / '   完整称呼: ', lv_full_name.

  " 字符串模板 - 现代方式
  DATA(lv_greeting) = |欢迎 { lv_student_name } 参加 { lv_course_name } 课程！|.
  WRITE: / '   欢迎信息: ', lv_greeting.

  " 字符串长度
  DATA(lv_name_length) = strlen( lv_student_name ).
  WRITE: / '   姓名长度: ', lv_name_length, ' 个字符'.

  WRITE: /.

*&---------------------------------------------------------------------*
*& 📚 第10课：数值运算 (Numeric Operations)
*&---------------------------------------------------------------------*

  " ✅ 练习10：数值计算
  " 任务：各种数学运算

  WRITE: / '🧮 数值运算示例:'.

  DATA: lv_num1 TYPE i VALUE 20,
        lv_num2 TYPE i VALUE 8.

  " 基本运算
  WRITE: / '   加法: ', lv_num1, ' + ', lv_num2, ' = ', lv_num1 + lv_num2.
  WRITE: / '   减法: ', lv_num1, ' - ', lv_num2, ' = ', lv_num1 - lv_num2.
  WRITE: / '   乘法: ', lv_num1, ' × ', lv_num2, ' = ', lv_num1 * lv_num2.
  WRITE: / '   除法: ', lv_num1, ' ÷ ', lv_num2, ' = ', lv_num1 / lv_num2.
  WRITE: / '   取余: ', lv_num1, ' MOD ', lv_num2, ' = ', lv_num1 MOD lv_num2.

  WRITE: /.

*&---------------------------------------------------------------------*
*& 🎊 程序结束
*&---------------------------------------------------------------------*

  WRITE: / '=======================================================',
         / '🎉 恭喜！您已完成 ABAP 基础语法学习！',
         / '=======================================================',
         / '',
         / '📚 下一步学习建议:',
         / '   1. 练习修改程序中的变量值',
         / '   2. 尝试添加新的计算逻辑',
         / '   3. 学习内表 (Internal Table) 操作',
         / '   4. 了解函数模块 (Function Module)',
         / '   5. 学习面向对象编程 (OOP)',
         / '',
         / '💡 记住：多练习是掌握 ABAP 的关键！'.

*&---------------------------------------------------------------------*
*& 🏠 课后练习题
*&---------------------------------------------------------------------*
*
* 📝 练习题 1: 修改学生信息
*    - 将学生姓名改为您的姓名
*    - 修改年龄和分数
*    - 观察输出结果的变化
*
* 📝 练习题 2: 添加新的条件判断
*    - 为年龄添加条件判断 (未成年/成年)
*    - 为GPA添加等级评定
*
* 📝 练习题 3: 扩展循环功能
*    - 修改倒计时的天数
*    - 计算不同范围的累加和 (如1到20)
*
* 📝 练习题 4: 字符串处理练习
*    - 创建包含多个信息的完整句子
*    - 尝试字符串大小写转换
*
* 📝 练习题 5: 数值计算扩展
*    - 添加更复杂的数学运算
*    - 计算平均值、最大值、最小值
*
*&---------------------------------------------------------------------*
