*&---------------------------------------------------------------------*
*& ABAP 基础语法练习文件
*& 学习目标：条件语句、循环、数据声明等基本编程元素
*&---------------------------------------------------------------------*

REPORT z_abap_basics_practice.

************************************************************************
* 1. 数据声明和处理
************************************************************************

"基本数据类型声明
DATA: lv_string TYPE string VALUE 'Hello ABAP',
      lv_char   TYPE c LENGTH 10 VALUE 'ABAP',
      lv_int    TYPE i VALUE 42,
      lv_packed TYPE p LENGTH 8 DECIMALS 2 VALUE '123.45'.

"内联声明（现代ABAP语法）
DATA(lv_inline_string) = 'Inline declaration'.
DATA(lv_inline_int) = 100.
DATA(lv_inline_date) = sy-datum.

"结构声明
TYPES: BEGIN OF ty_employee,
         id     TYPE i,
         name   TYPE string,
         salary TYPE p LENGTH 8 DECIMALS 2,
       END OF ty_employee.

DATA: ls_employee TYPE ty_employee,
      lt_employees TYPE TABLE OF ty_employee.

"内表声明
DATA: lt_string_table TYPE TABLE OF string,
      lt_int_table TYPE STANDARD TABLE OF i WITH EMPTY KEY.

************************************************************************
* 2. 条件语句练习
************************************************************************

"IF语句基础练习
WRITE: / '=== IF语句练习 ==='.
DATA(lv_number) = 5.

IF lv_number = 5.
  WRITE: / '数字等于5'.
ELSE.
  WRITE: / '数字不等于5'.
ENDIF.

"多条件IF语句
IF lv_number > 0 AND lv_number < 10.
  WRITE: / '数字在0到10之间'.
ENDIF.

"IF-ELSEIF-ELSE结构
CASE lv_number.
  WHEN 1.
    WRITE: / '数字是1'.
  WHEN 2 OR 3.
    WRITE: / '数字是2或3'.
  WHEN 4 TO 6.
    WRITE: / '数字在4到6之间'.
  WHEN OTHERS.
    WRITE: / '其他数字'.
ENDCASE.

************************************************************************
* 3. 循环语句练习
************************************************************************

WRITE: /, '=== 循环语句练习 ==='.

"DO循环
WRITE: / 'DO循环示例:'.
DO 5 TIMES.
  WRITE: / '循环次数:', sy-index.
ENDDO.

"WHILE循环
DATA(lv_counter) = 1.
WRITE: / 'WHILE循环示例:'.
WHILE lv_counter <= 3.
  WRITE: / '计数器值:', lv_counter.
  lv_counter = lv_counter + 1.
ENDDO.

"LOOP循环 - 内表处理
WRITE: / 'LOOP循环示例:'.

"填充内表
lt_string_table = VALUE #( ( '第一条记录' )
                           ( '第二条记录' )
                           ( '第三条记录' ) ).

LOOP AT lt_string_table INTO DATA(lv_table_line).
  WRITE: / '表记录:', lv_table_line, ' 索引:', sy-tabix.
ENDLOOP.

************************************************************************
* 4. 字符串处理练习
************************************************************************

WRITE: /, '=== 字符串处理练习 ==='.

DATA(lv_text1) = 'Hello'.
DATA(lv_text2) = 'World'.
DATA(lv_combined) = |{ lv_text1 } { lv_text2 }!|.

WRITE: / '连接字符串:', lv_combined.

"字符串长度
WRITE: / '字符串长度:', strlen( lv_combined ).

"查找子字符串
FIND 'World' IN lv_combined.
IF sy-subrc = 0.
  WRITE: / '找到子字符串'.
ELSE.
  WRITE: / '未找到子字符串'.
ENDIF.

************************************************************************
* 5. 数值运算练习
************************************************************************

WRITE: /, '=== 数值运算练习 ==='.

DATA(lv_result) = 0.
lv_result = 10 + 5.
WRITE: / '加法: 10 + 5 =', lv_result.

lv_result = 10 - 3.
WRITE: / '减法: 10 - 3 =', lv_result.

lv_result = 4 * 6.
WRITE: / '乘法: 4 * 6 =', lv_result.

lv_result = 15 / 3.
WRITE: / '除法: 15 / 3 =', lv_result.

************************************************************************
* 6. 参数处理练习
************************************************************************

WRITE: /, '=== 参数处理练习 ==='.

PARAMETERS: p_name TYPE string OBLIGATORY DEFAULT '请输入姓名',
            p_age  TYPE i DEFAULT 25,
            p_city TYPE string DEFAULT '北京'.

START-OF-SELECTION.
  WRITE: / '姓名:', p_name.
  WRITE: / '年龄:', p_age.
  WRITE: / '城市:', p_city.

************************************************************************
* 7. 综合练习 - 员工管理系统模拟
************************************************************************

WRITE: /, '=== 综合练习 ==='.

"创建员工数据
lt_employees = VALUE #(
  ( id = 1 name = '张三' salary = '5000.00' )
  ( id = 2 name = '李四' salary = '6000.00' )
  ( id = 3 name = '王五' salary = '5500.00' )
).

"显示员工信息
LOOP AT lt_employees INTO ls_employee.
  WRITE: / '员工ID:', ls_employee-id,
         ' 姓名:', ls_employee-name,
         ' 薪资:', ls_employee-salary.
ENDLOOP.

"查找特定员工
READ TABLE lt_employees INTO ls_employee WITH KEY id = 2.
IF sy-subrc = 0.
  WRITE: / '找到员工:', ls_employee-name.
ENDIF.

"计算平均薪资
DATA(lv_total_salary) = 0.
DATA(lv_employee_count) = 0.

LOOP AT lt_employees INTO ls_employee.
  lv_total_salary = lv_total_salary + ls_employee-salary.
  lv_employee_count = lv_employee_count + 1.
ENDLOOP.

IF lv_employee_count > 0.
  DATA(lv_avg_salary) = lv_total_salary / lv_employee_count.
  WRITE: / '平均薪资:', lv_avg_salary.
ENDIF.

************************************************************************
* 8. 异常处理练习
************************************************************************

WRITE: /, '=== 异常处理练习 ==='.

TRY.
    DATA(lv_division) = 10 / 0.
  CATCH cx_sy_zerodivide.
    WRITE: / '捕获到除零异常'.
ENDTRY.

************************************************************************
* 9. 系统变量练习
************************************************************************

WRITE: /, '=== 系统变量练习 ==='.
WRITE: / '当前日期:', sy-datum.
WRITE: / '当前时间:', sy-uzeit.
WRITE: / '用户名:', sy-uname.
WRITE: / '语言:', sy-langu.
WRITE: / '客户端:', sy-mandt.

************************************************************************
* 10. 练习题
************************************************************************

WRITE: /, '=== 练习题 ==='.
WRITE: / '1. 创建一个程序，计算1到100的和'.
WRITE: / '2. 创建一个程序，判断输入的数字是奇数还是偶数'.
WRITE: / '3. 创建一个程序，处理员工数据表，按薪资排序'.
WRITE: / '4. 创建一个程序，使用CASE语句处理不同的用户输入'.
WRITE: / '5. 创建一个程序，演示字符串的各种处理函数'.

WRITE: /, '=== 练习完成 ==='.

************************************************************************
* 练习题答案示例
************************************************************************

WRITE: /, '=== 练习题答案示例 ==='.

"练习题1：计算1到100的和
DATA(lv_sum) = 0.
DO 100 TIMES.
  lv_sum = lv_sum + sy-index.
ENDDO.
WRITE: / '1到100的和:', lv_sum.

"练习题2：判断奇偶数
DATA(lv_input_number) = 7.
IF lv_input_number MOD 2 = 0.
  WRITE: / lv_input_number, '是偶数'.
ELSE.
  WRITE: / lv_input_number, '是奇数'.
ENDIF.

"练习题4：CASE语句处理用户输入
DATA(lv_user_choice) = 'B'.
CASE lv_user_choice.
  WHEN 'A'.
    WRITE: / '用户选择了选项A'.
  WHEN 'B'.
    WRITE: / '用户选择了选项B'.
  WHEN 'C'.
    WRITE: / '用户选择了选项C'.
  WHEN OTHERS.
    WRITE: / '无效选择'.
ENDCASE.
