*&---------------------------------------------------------------------*
*& ABAP 个人练习文件
*& 在这里编写您的练习代码
*&---------------------------------------------------------------------*

REPORT z_my_abap_practice.

************************************************************************
* 请在这里开始您的ABAP练习
************************************************************************

"1. 从简单的数据声明开始
DATA: BEGIN OF gs_student,
        id     TYPE i,
        name   TYPE string,
        age    TYPE i,
        grade  TYPE c LENGTH 2,
      END OF gs_student.

DATA: gt_students TYPE TABLE OF gs_student.

"2. 练习条件语句
DATA(lv_score) = 85.

IF lv_score >= 90.
  WRITE: / '优秀'.
ELSEIF lv_score >= 80.
  WRITE: / '良好'.
ELSEIF lv_score >= 60.
  WRITE: / '及格'.
ELSE.
  WRITE: / '不及格'.
ENDIF.

"3. 练习循环语句
WRITE: /, '=== 我的循环练习 ==='.

"在这里添加您的循环练习代码...

"4. 练习内表操作
WRITE: /, '=== 我的内表练习 ==='.

"在这里添加您的内表练习代码...

"5. 练习字符串处理
WRITE: /, '=== 我的字符串练习 ==='.

"在这里添加您的字符串练习代码...

"6. 练习函数调用
WRITE: /, '=== 我的函数练习 ==='.

"在这里添加您的函数练习代码...

************************************************************************
* 练习区域
************************************************************************

"请在这里添加您的练习代码...

************************************************************************
* 练习题完成情况记录
************************************************************************

WRITE: /, '=== 练习完成记录 ==='.
WRITE: / '□ 练习题1：计算1到100的和'.
WRITE: / '□ 练习题2：判断奇偶数'.
WRITE: / '□ 练习题3：员工数据处理'.
WRITE: / '□ 练习题4：CASE语句应用'.
WRITE: / '□ 练习题5：字符串处理'.

************************************************************************
* 学习笔记
************************************************************************

* 今天学到的知识点：
* 1. ________________________________
* 2. ________________________________
* 3. ________________________________

* 遇到的问题和解决方案：
* 问题：_________________________
* 解决：_________________________

* 需要进一步学习的内容：
* 1. ________________________________
* 2. ________________________________
