# 文本文件分割工具

## 功能说明
这是一个Python脚本，用于将大型txt文件按20MB大小进行分割，同时保持每行的完整性，不会将一行内容分割到两个文件中。

## 特点
- 📏 按20MB大小自动分割文件
- ✅ 保持行的完整性，不会分割单行内容
- 📁 支持单个文件分割和目录批量分割
- 🌐 完美支持中文字符编码
- 📊 实时显示处理进度
- 🐍 纯Python实现，无需额外依赖

## 使用方法

### 1. 分割单个文件
```bash
python split_txt_files.py your_file.txt
```

### 2. 分割目录下所有txt文件
```bash
python split_txt_files.py /path/to/directory -d
```

### 3. 使用示例
```bash
# 分割单个文件
python split_txt_files.py large_document.txt

# 分割目录下所有txt文件
python split_txt_files.py ./documents -d
```

## 输出文件命名规则
分割后的文件将按照以下规则命名：
- 原文件名: `document.txt`
- 分割后: `document_part001.txt`, `document_part002.txt`, ...

## 技术细节

### 分割逻辑
1. 按20MB（20,971,520字节）大小限制分割
2. 逐行读取，确保行的完整性
3. 当添加新行会超过大小限制时，将该行保留到下一个文件
4. 自动生成序号文件名

### 编码支持
- 使用二进制模式读取文件，避免编码问题
- 支持UTF-8、GBK等常见编码格式
- 完美处理中英文混合文本

### 错误处理
- 自动检查文件是否存在
- 验证文件格式（仅处理.txt文件）
- 异常情况下的错误提示

## 使用场景
- 📚 大型日志文件分析
- 📄 超大文本文件处理
- 💾 文件上传大小限制突破
- 🔄 数据迁移预处理
- 📊 批量文本处理

## 注意事项
- 原始文件不会被自动删除
- 分割过程不会修改原始文件
- 确保磁盘空间足够存储分割后的文件
- 处理超大文件时可能需要较长时间

## 系统要求
- Python 3.6+
- 无额外依赖包

## 脚本作者
Cline - 2025-07-28

## 许可证
MIT License
