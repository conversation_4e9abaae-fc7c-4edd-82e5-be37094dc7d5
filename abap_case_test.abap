*&---------------------------------------------------------------------*
*& ABAP 关键词大小写测试程序
*&---------------------------------------------------------------------*
*& 证明 ABAP 关键词不区分大小写
*&---------------------------------------------------------------------*

REPORT z_case_test.

*&---------------------------------------------------------------------*
*& 测试1：全大写关键词（传统风格）
*&---------------------------------------------------------------------*
DATA: LV_NAME_UPPER TYPE STRING VALUE 'UPPER_CASE_STYLE'.
CONSTANTS: LC_VERSION_UPPER TYPE STRING VALUE 'V1.0_UPPER'.

*&---------------------------------------------------------------------*
*& 测试2：全小写关键词（现代风格）
*&---------------------------------------------------------------------*
data: lv_name_lower type string value 'lower_case_style'.
constants: lc_version_lower type string value 'v1.0_lower'.

*&---------------------------------------------------------------------*
*& 测试3：混合大小写关键词
*&---------------------------------------------------------------------*
Data: lv_name_mixed Type string Value 'Mixed_Case_Style'.
Constants: lc_version_mixed Type string Value 'V1.0_Mixed'.

*&---------------------------------------------------------------------*
*& 测试4：随意大小写关键词
*&---------------------------------------------------------------------*
dAtA: lv_name_random TyPe string VaLuE 'Random_Case_Style'.
cOnStAnTs: lc_version_random TyPe string VaLuE 'V1.0_Random'.

*&---------------------------------------------------------------------*
*& 主程序
*&---------------------------------------------------------------------*
START-OF-SELECTION.

  " 输出测试结果 - 全大写风格
  WRITE: / '=== 全大写关键词测试 ===',
         / 'LV_NAME_UPPER: ', LV_NAME_UPPER,
         / 'LC_VERSION_UPPER: ', LC_VERSION_UPPER,
         /.

  " 输出测试结果 - 全小写风格
  write: / '=== 全小写关键词测试 ===',
         / 'lv_name_lower: ', lv_name_lower,
         / 'lc_version_lower: ', lc_version_lower,
         /.

  " 输出测试结果 - 混合大小写风格
  Write: / '=== 混合大小写关键词测试 ===',
         / 'lv_name_mixed: ', lv_name_mixed,
         / 'lc_version_mixed: ', lc_version_mixed,
         /.

  " 输出测试结果 - 随意大小写风格
  wRiTe: / '=== 随意大小写关键词测试 ===',
         / 'lv_name_random: ', lv_name_random,
         / 'lc_version_random: ', lc_version_random,
         /.

  " 条件判断测试 - 不同大小写风格
  IF LV_NAME_UPPER IS NOT INITIAL.
    WRITE: / '✅ 大写 IF 语句工作正常'.
  ENDIF.

  if lv_name_lower is not initial.
    write: / '✅ 小写 if 语句工作正常'.
  endif.

  If lv_name_mixed Is Not Initial.
    Write: / '✅ 混合大小写 If 语句工作正常'.
  EndIf.

  " 循环测试 - 不同大小写风格
  DO 2 TIMES.
    WRITE: / '大写 DO 循环，第', SY-INDEX, '次'.
  ENDDO.

  do 2 times.
    write: / '小写 do 循环，第', sy-index, '次'.
  enddo.

  Do 2 Times.
    Write: / '混合 Do 循环，第', sy-index, '次'.
  EndDo.

  WRITE: / '',
         / '🎉 结论：所有大小写风格都能正常工作！',
         / '💡 ABAP 关键词完全不区分大小写！'.

*&---------------------------------------------------------------------*
*& 程序结束
*&---------------------------------------------------------------------*
