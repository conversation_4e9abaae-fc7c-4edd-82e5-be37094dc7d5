#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ABAP 语法模拟器
作者: ABAP 学习助手
描述: 用 Python 模拟 ABAP 语法，帮助学习 ABAP 编程概念
"""

import datetime
import os
from typing import Any, Dict, List

class ABAPSimulator:
    """ABAP 语法模拟器类"""
    
    def __init__(self):
        """初始化模拟器"""
        self.variables = {}  # 存储变量
        self.constants = {}  # 存储常量
        self.sy_variables = self._init_system_variables()  # 系统变量
        self.output_lines = []  # 输出行
        
    def _init_system_variables(self) -> Dict[str, Any]:
        """初始化系统变量 (sy-*)"""
        return {
            'sy-datum': datetime.date.today().strftime('%Y%m%d'),  # 当前日期
            'sy-uzeit': datetime.datetime.now().strftime('%H%M%S'),  # 当前时间
            'sy-uname': os.getenv('USERNAME', 'ABAP_LEARNER'),  # 用户名
            'sy-index': 0,  # 循环索引
            'sy-tabix': 0,  # 表索引
            'sy-subrc': 0   # 返回码
        }
    
    def data_declaration(self, var_name: str, var_type: str, value: Any = None):
        """模拟 DATA 声明"""
        print(f"📝 声明变量: {var_name} TYPE {var_type}")
        if value is not None:
            self.variables[var_name] = value
            print(f"   初始值: {value}")
        else:
            # 根据类型设置默认值
            if var_type.lower() in ['string', 'c']:
                self.variables[var_name] = ''
            elif var_type.lower() in ['i', 'integer']:
                self.variables[var_name] = 0
            elif var_type.lower() == 'd':
                self.variables[var_name] = '00000000'
            elif var_type.lower() == 't':
                self.variables[var_name] = '000000'
        print()
    
    def constants_declaration(self, const_name: str, const_type: str, value: Any):
        """模拟 CONSTANTS 声明"""
        print(f"🔒 声明常量: {const_name} TYPE {const_type} VALUE '{value}'")
        self.constants[const_name] = value
        print()
    
    def write_statement(self, *args):
        """模拟 WRITE 语句"""
        output_line = ""
        for arg in args:
            if isinstance(arg, str):
                if arg.startswith('/'):
                    # 新行
                    if output_line:
                        self.output_lines.append(output_line)
                        print(output_line)
                    output_line = arg[1:].strip()
                else:
                    output_line += str(arg)
            else:
                # 变量或常量
                if arg in self.variables:
                    output_line += str(self.variables[arg])
                elif arg in self.constants:
                    output_line += str(self.constants[arg])
                elif arg in self.sy_variables:
                    output_line += str(self.sy_variables[arg])
                else:
                    output_line += str(arg)
        
        if output_line:
            self.output_lines.append(output_line)
            print(output_line)
    
    def if_statement(self, condition: bool, true_action, false_action=None):
        """模拟 IF 语句"""
        print(f"🔍 条件判断: {condition}")
        if condition:
            print("   ✅ 条件为真，执行 IF 分支")
            true_action()
        else:
            print("   ❌ 条件为假", end="")
            if false_action:
                print("，执行 ELSE 分支")
                false_action()
            else:
                print()
        print()
    
    def do_loop(self, times: int, action):
        """模拟 DO 循环"""
        print(f"🔄 开始 DO 循环 {times} 次:")
        for i in range(1, times + 1):
            self.sy_variables['sy-index'] = i
            print(f"   循环第 {i} 次:")
            action(i)
        print("🔄 DO 循环结束\n")
    
    def while_loop(self, condition_func, action):
        """模拟 WHILE 循环"""
        print("🔄 开始 WHILE 循环:")
        count = 0
        while condition_func():
            count += 1
            self.sy_variables['sy-index'] = count
            print(f"   循环第 {count} 次:")
            action(count)
            if count > 100:  # 防止无限循环
                print("   ⚠️ 循环次数过多，自动终止")
                break
        print("🔄 WHILE 循环结束\n")
    
    def case_statement(self, variable, cases: Dict[Any, callable], default_action=None):
        """模拟 CASE 语句"""
        value = self.get_variable_value(variable)
        print(f"🎯 CASE 语句，变量 {variable} = {value}")
        
        if value in cases:
            print(f"   ✅ 匹配到 WHEN '{value}'")
            cases[value]()
        else:
            print("   ❓ 没有匹配的条件", end="")
            if default_action:
                print("，执行 WHEN OTHERS")
                default_action()
            else:
                print()
        print()
    
    def get_variable_value(self, var_name: str) -> Any:
        """获取变量值"""
        if var_name in self.variables:
            return self.variables[var_name]
        elif var_name in self.constants:
            return self.constants[var_name]
        elif var_name in self.sy_variables:
            return self.sy_variables[var_name]
        else:
            return None
    
    def set_variable_value(self, var_name: str, value: Any):
        """设置变量值"""
        if var_name in self.variables:
            self.variables[var_name] = value
            print(f"📝 设置变量 {var_name} = {value}")
        else:
            print(f"❌ 变量 {var_name} 未声明")
    
    def start_of_selection(self):
        """模拟 START-OF-SELECTION 事件"""
        print("🚀 START-OF-SELECTION 事件开始")
        print("=" * 50)
    
    def end_of_selection(self):
        """模拟 END-OF-SELECTION 事件"""
        print("=" * 50)
        print("🏁 程序执行完成")

def demo_abap_program():
    """演示 ABAP 程序"""
    # 创建模拟器实例
    abap = ABAPSimulator()
    
    print("🎉 欢迎使用 ABAP 语法模拟器！")
    print("=" * 50)
    print()
    
    # 模拟程序开始
    abap.start_of_selection()
    print()
    
    # 数据声明
    print("📊 数据声明部分:")
    abap.data_declaration('lv_message', 'string', 'Hello ABAP World!')
    abap.data_declaration('lv_number', 'i', 42)
    abap.data_declaration('lv_counter', 'i', 0)
    
    # 常量声明
    print("🔒 常量声明部分:")
    abap.constants_declaration('lc_company', 'string', 'SAP学习公司')
    abap.constants_declaration('lc_version', 'string', 'V1.0')
    
    # 输出基本信息
    print("📤 输出语句演示:")
    abap.write_statement('/', '🎯 程序信息:')
    abap.write_statement('/', '   公司名称: ', 'lc_company')
    abap.write_statement('/', '   程序版本: ', 'lc_version')
    abap.write_statement('/', '   欢迎消息: ', 'lv_message')
    abap.write_statement('/', '')
    
    # 系统变量演示
    print("\n🖥️ 系统变量演示:")
    abap.write_statement('/', '系统信息:')
    abap.write_statement('/', '   当前用户: ', 'sy-uname')
    abap.write_statement('/', '   系统日期: ', 'sy-datum')
    abap.write_statement('/', '   系统时间: ', 'sy-uzeit')
    abap.write_statement('/', '')
    
    # 条件判断演示
    print("\n🔍 条件判断演示:")
    def true_action():
        abap.write_statement('/', '   ✅ 数值大于 40')
    
    def false_action():
        abap.write_statement('/', '   ❌ 数值不大于 40')
    
    number_value = abap.get_variable_value('lv_number')
    abap.if_statement(number_value > 40, true_action, false_action)
    
    # DO 循环演示
    print("🔄 DO 循环演示:")
    def loop_action(index):
        abap.write_statement(f'      第 {index} 次循环')
    
    abap.do_loop(5, loop_action)
    
    # CASE 语句演示
    print("🎯 CASE 语句演示:")
    abap.data_declaration('lv_grade', 'c', 'A')
    
    def case_a():
        abap.write_statement('/', '   🌟 优秀成绩！')
    
    def case_b():
        abap.write_statement('/', '   👍 良好成绩！')
    
    def case_default():
        abap.write_statement('/', '   📚 继续努力！')
    
    cases = {'A': case_a, 'B': case_b}
    abap.case_statement('lv_grade', cases, case_default)
    
    # 程序结束
    abap.end_of_selection()

if __name__ == "__main__":
    demo_abap_program()
