#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ABAP 语法练习训练器
作者: ABAP 学习助手
描述: 先学习文档，然后练习编写真正的 ABAP 语句
"""

import re
import datetime
import os
from typing import Dict, List, Any, Tuple

class ABAPSyntaxTrainer:
    """ABAP 语法练习训练器"""
    
    def __init__(self):
        """初始化训练器"""
        self.variables = {}  # 存储已声明的变量
        self.constants = {}  # 存储已声明的常量
        self.sy_variables = self._init_system_variables()
        self.current_lesson = 1
        
    def _init_system_variables(self) -> Dict[str, Any]:
        """初始化系统变量"""
        return {
            'sy-datum': datetime.date.today().strftime('%Y%m%d'),
            'sy-uzeit': datetime.datetime.now().strftime('%H%M%S'),
            'sy-uname': os.getenv('USERNAME', 'ABAP_LEARNER'),
            'sy-index': 0,
            'sy-tabix': 0,
            'sy-subrc': 0
        }
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("🎓" + "=" * 60 + "🎓")
        print("📚          ABAP 语法练习训练器          📚")
        print("🎓" + "=" * 60 + "🎓")
        print()
        print("💡 学习方式：")
        print("   1️⃣ 首先阅读语法文档和示例")
        print("   2️⃣ 然后您亲自编写 ABAP 语句")
        print("   3️⃣ 训练器检查语法并执行代码")
        print("   4️⃣ 获得实时反馈和错误提示")
        print()
        print("🚀 让我们开始 ABAP 语法学习之旅！")
        print()
    
    def show_lesson_menu(self):
        """显示课程菜单"""
        print("📋" + "=" * 50 + "📋")
        print("🎯            ABAP 语法课程菜单            🎯")
        print("📋" + "=" * 50 + "📋")
        print()
        print("1️⃣  数据声明语法 (DATA Statement)")
        print("2️⃣  常量声明语法 (CONSTANTS Statement)")
        print("3️⃣  输出语句语法 (WRITE Statement)")
        print("4️⃣  条件判断语法 (IF-ELSE Statement)")
        print("5️⃣  CASE 语句语法 (CASE Statement)")
        print("6️⃣  DO 循环语法 (DO Loop)")
        print("7️⃣  WHILE 循环语法 (WHILE Loop)")
        print("8️⃣  字符串操作语法 (String Operations)")
        print("9️⃣  数值运算语法 (Numeric Operations)")
        print("🔟  综合练习 (Comprehensive Practice)")
        print("0️⃣  退出程序 (Exit)")
        print()
    
    def lesson_data_declaration(self):
        """第1课：数据声明语法"""
        print("📚" + "=" * 60 + "📚")
        print("📝              第1课：数据声明语法              📝")
        print("📚" + "=" * 60 + "📚")
        print()
        
        # 文档学习部分
        print("📖 **语法文档学习**")
        print("=" * 50)
        print()
        
        print("💡 **DATA 语句用途：**")
        print("   - 声明变量并分配内存空间")
        print("   - 指定变量的数据类型")
        print("   - 可选择性地设置初始值")
        print()
        
        print("📝 **基本语法格式：**")
        print("   DATA: 变量名 TYPE 数据类型 [VALUE 初始值].")
        print("   或")
        print("   DATA 变量名 TYPE 数据类型 [VALUE 初始值].")
        print()

        print("🔤 **关键词大小写说明：**")
        print("   - ABAP 关键词不区分大小写")
        print("   - 传统风格：DATA、TYPE、VALUE (全大写)")
        print("   - 现代风格：data、type、value (全小写)")
        print("   - 两种写法完全等价，选择一种保持一致即可")
        print()
        
        print("🔤 **常用数据类型：**")
        print("   - STRING    : 字符串类型")
        print("   - I         : 整数类型 (4字节)")
        print("   - F         : 浮点数类型")
        print("   - D         : 日期类型 (YYYYMMDD)")
        print("   - T         : 时间类型 (HHMMSS)")
        print("   - C(长度)   : 固定长度字符类型")
        print("   - N(长度)   : 数字字符类型")
        print("   - P(长度)   : 压缩数字类型")
        print()
        
        print("✨ **语法示例（现代风格 - 推荐）：**")
        print("   data: lv_name type string value 'SAP学习者'.")
        print("   data: lv_age type i value 25.")
        print("   data: lv_salary type p decimals 2 value '5000.50'.")
        print("   data: lv_date type d value '20250801'.")
        print("   data: lv_flag type c length 1 value 'X'.")
        print()

        print("✨ **语法示例（传统风格）：**")
        print("   DATA: LV_NAME TYPE STRING VALUE 'SAP学习者'.")
        print("   DATA: LV_AGE TYPE I VALUE 25.")
        print("   DATA: LV_SALARY TYPE P DECIMALS 2 VALUE '5000.50'.")
        print("   DATA: LV_DATE TYPE D VALUE '20250801'.")
        print("   DATA: LV_FLAG TYPE C LENGTH 1 VALUE 'X'.")
        print()
        
        print("🔍 **现代 ABAP 内联声明：**")
        print("   DATA(lv_message) = 'Hello ABAP'.")
        print("   DATA(lv_number) = 42.")
        print()
        
        print("⚠️ **注意事项：**")
        print("   - 变量名建议使用前缀：lv_(local variable)")
        print("   - 语句必须以句点(.)结束")
        print("   - 字符串值用单引号包围")
        print("   - 日期格式必须是 YYYYMMDD")
        print()
        
        # 练习部分
        print("🎯 **现在开始练习！**")
        print("=" * 50)
        print()
        
        exercises = [
            {
                "task": "声明一个字符串变量 lv_student_name，初始值为您的姓名",
                "hint": "DATA: lv_student_name TYPE string VALUE '您的姓名'.",
                "expected_pattern": r"DATA:\s*lv_student_name\s+TYPE\s+string\s+VALUE\s+'[^']+'\s*\."
            },
            {
                "task": "声明一个整数变量 lv_student_age，初始值为您的年龄",
                "hint": "DATA: lv_student_age TYPE i VALUE 年龄数字.",
                "expected_pattern": r"DATA:\s*lv_student_age\s+TYPE\s+i\s+VALUE\s+\d+\s*\."
            },
            {
                "task": "声明一个日期变量 lv_birth_date，初始值为今天的日期",
                "hint": "DATA: lv_birth_date TYPE d VALUE '20250801'.",
                "expected_pattern": r"DATA:\s*lv_birth_date\s+TYPE\s+d\s+VALUE\s+'\d{8}'\s*\."
            },
            {
                "task": "使用现代语法声明一个变量 lv_company，值为 'SAP公司'",
                "hint": "DATA(lv_company) = 'SAP公司'.",
                "expected_pattern": r"DATA\(lv_company\)\s*=\s*'[^']+'\s*\."
            }
        ]
        
        for i, exercise in enumerate(exercises, 1):
            print(f"📝 **练习 {i}：**")
            print(f"   任务：{exercise['task']}")
            print(f"   提示：{exercise['hint']}")
            print()
            
            while True:
                user_input = input("请输入您的 ABAP 语句: ").strip()
                
                if not user_input:
                    print("❌ 输入不能为空，请重新输入！")
                    continue
                
                # 检查语法
                result = self._check_data_syntax(user_input, exercise['expected_pattern'])
                
                if result['valid']:
                    print("✅ 语法正确！")
                    print(f"📊 执行结果：{result['message']}")
                    print()
                    break
                else:
                    print(f"❌ 语法错误：{result['error']}")
                    print("💡 请参考提示重新输入")
                    print()
        
        print("🎉 恭喜！您已完成数据声明语法练习！")
        print()
        input("按 Enter 键继续...")
    
    def _check_data_syntax(self, statement: str, expected_pattern: str) -> Dict[str, Any]:
        """检查 DATA 语句语法"""
        try:
            # 基本格式检查
            if not statement.endswith('.'):
                return {"valid": False, "error": "ABAP 语句必须以句点(.)结束"}
            
            # 使用正则表达式检查语法
            if not re.match(expected_pattern, statement, re.IGNORECASE):
                return {"valid": False, "error": "语法格式不正确，请检查拼写和格式"}
            
            # 解析语句
            if statement.upper().startswith('DATA:'):
                # 传统语法
                match = re.match(r'DATA:\s*(\w+)\s+TYPE\s+(\w+)(?:\s+VALUE\s+(.+?))?\s*\.', statement, re.IGNORECASE)
                if match:
                    var_name, var_type, var_value = match.groups()
                    if var_value:
                        var_value = var_value.strip("'\"")
                    else:
                        var_value = self._get_default_value(var_type)
                    
                    self.variables[var_name] = var_value
                    return {
                        "valid": True, 
                        "message": f"变量 {var_name} 已声明，类型：{var_type}，值：{var_value}"
                    }
            
            elif statement.upper().startswith('DATA('):
                # 现代语法
                match = re.match(r'DATA\((\w+)\)\s*=\s*(.+?)\s*\.', statement, re.IGNORECASE)
                if match:
                    var_name, var_value = match.groups()
                    var_value = var_value.strip("'\"")
                    self.variables[var_name] = var_value
                    return {
                        "valid": True,
                        "message": f"变量 {var_name} 已声明，值：{var_value}"
                    }
            
            return {"valid": False, "error": "无法解析语句，请检查语法"}
            
        except Exception as e:
            return {"valid": False, "error": f"语法分析错误：{str(e)}"}
    
    def _get_default_value(self, data_type: str) -> Any:
        """获取数据类型的默认值"""
        type_defaults = {
            'string': '',
            'i': 0,
            'f': 0.0,
            'd': '00000000',
            't': '000000',
            'c': '',
            'n': '0',
            'p': 0
        }
        return type_defaults.get(data_type.lower(), '')
    
    def lesson_constants_declaration(self):
        """第2课：常量声明语法"""
        print("📚" + "=" * 60 + "📚")
        print("🔒              第2课：常量声明语法              🔒")
        print("📚" + "=" * 60 + "📚")
        print()
        
        # 文档学习部分
        print("📖 **语法文档学习**")
        print("=" * 50)
        print()
        
        print("💡 **CONSTANTS 语句用途：**")
        print("   - 声明程序中不会改变的值")
        print("   - 提高代码的可读性和维护性")
        print("   - 避免魔法数字和硬编码")
        print()
        
        print("📝 **基本语法格式：**")
        print("   CONSTANTS: 常量名 TYPE 数据类型 VALUE 值.")
        print("   或")
        print("   CONSTANTS 常量名 TYPE 数据类型 VALUE 值.")
        print()
        
        print("✨ **语法示例：**")
        print("   CONSTANTS: lc_max_score TYPE i VALUE 100.")
        print("   CONSTANTS: lc_company TYPE string VALUE 'SAP公司'.")
        print("   CONSTANTS: lc_pi TYPE f VALUE '3.14159'.")
        print("   CONSTANTS: lc_version TYPE c LENGTH 5 VALUE 'V2.0'.")
        print()
        
        print("🏷️ **命名约定：**")
        print("   - 常量名建议使用前缀：lc_(local constant)")
        print("   - 使用有意义的名称")
        print("   - 全大写或驼峰命名法")
        print()
        
        print("⚠️ **注意事项：**")
        print("   - 常量必须在声明时赋值")
        print("   - 常量值在程序运行期间不能修改")
        print("   - VALUE 子句是必需的")
        print()
        
        # 练习部分
        print("🎯 **现在开始练习！**")
        print("=" * 50)
        print()
        
        exercises = [
            {
                "task": "声明一个整数常量 lc_max_students，值为 30",
                "hint": "CONSTANTS: lc_max_students TYPE i VALUE 30.",
                "expected_pattern": r"CONSTANTS:\s*lc_max_students\s+TYPE\s+i\s+VALUE\s+30\s*\."
            },
            {
                "task": "声明一个字符串常量 lc_school_name，值为您的学校名称",
                "hint": "CONSTANTS: lc_school_name TYPE string VALUE '学校名称'.",
                "expected_pattern": r"CONSTANTS:\s*lc_school_name\s+TYPE\s+string\s+VALUE\s+'[^']+'\s*\."
            },
            {
                "task": "声明一个浮点数常量 lc_pass_rate，值为 0.6",
                "hint": "CONSTANTS: lc_pass_rate TYPE f VALUE '0.6'.",
                "expected_pattern": r"CONSTANTS:\s*lc_pass_rate\s+TYPE\s+f\s+VALUE\s+'?0\.6'?\s*\."
            }
        ]
        
        for i, exercise in enumerate(exercises, 1):
            print(f"📝 **练习 {i}：**")
            print(f"   任务：{exercise['task']}")
            print(f"   提示：{exercise['hint']}")
            print()
            
            while True:
                user_input = input("请输入您的 ABAP 语句: ").strip()
                
                if not user_input:
                    print("❌ 输入不能为空，请重新输入！")
                    continue
                
                # 检查语法
                result = self._check_constants_syntax(user_input, exercise['expected_pattern'])
                
                if result['valid']:
                    print("✅ 语法正确！")
                    print(f"📊 执行结果：{result['message']}")
                    print()
                    break
                else:
                    print(f"❌ 语法错误：{result['error']}")
                    print("💡 请参考提示重新输入")
                    print()
        
        print("🎉 恭喜！您已完成常量声明语法练习！")
        print()
        input("按 Enter 键继续...")
    
    def _check_constants_syntax(self, statement: str, expected_pattern: str) -> Dict[str, Any]:
        """检查 CONSTANTS 语句语法"""
        try:
            # 基本格式检查
            if not statement.endswith('.'):
                return {"valid": False, "error": "ABAP 语句必须以句点(.)结束"}
            
            if not statement.upper().startswith('CONSTANTS'):
                return {"valid": False, "error": "常量声明必须以 CONSTANTS 开头"}
            
            # 使用正则表达式检查语法
            if not re.match(expected_pattern, statement, re.IGNORECASE):
                return {"valid": False, "error": "语法格式不正确，请检查拼写和格式"}
            
            # 解析语句
            match = re.match(r'CONSTANTS:\s*(\w+)\s+TYPE\s+(\w+)(?:\s+LENGTH\s+\d+)?\s+VALUE\s+(.+?)\s*\.', statement, re.IGNORECASE)
            if match:
                const_name, const_type, const_value = match.groups()
                const_value = const_value.strip("'\"")
                
                self.constants[const_name] = const_value
                return {
                    "valid": True,
                    "message": f"常量 {const_name} 已声明，类型：{const_type}，值：{const_value}"
                }
            
            return {"valid": False, "error": "无法解析语句，请检查语法"}
            
        except Exception as e:
            return {"valid": False, "error": f"语法分析错误：{str(e)}"}
    
    def lesson_write_statement(self):
        """第3课：输出语句语法"""
        print("📚" + "=" * 60 + "📚")
        print("📤              第3课：输出语句语法              📤")
        print("📚" + "=" * 60 + "📚")
        print()
        
        # 文档学习部分
        print("📖 **语法文档学习**")
        print("=" * 50)
        print()
        
        print("💡 **WRITE 语句用途：**")
        print("   - 将数据输出到屏幕")
        print("   - 显示变量值和文本")
        print("   - 格式化输出内容")
        print()
        
        print("📝 **基本语法格式：**")
        print("   WRITE: 输出内容.")
        print("   WRITE 输出内容.")
        print()
        
        print("✨ **语法示例：**")
        print("   WRITE: 'Hello ABAP World!'.")
        print("   WRITE: '姓名:', lv_name.")
        print("   WRITE: / '新行输出'.")
        print("   WRITE: lv_age, '岁'.")
        print()
        
        print("🎨 **格式控制：**")
        print("   - /        : 换行")
        print("   - AT n     : 在第n列输出")
        print("   - UNDER    : 在上一行相同位置输出")
        print("   - NO-GAP   : 无间隔输出")
        print()
        
        print("📋 **输出示例：**")
        print("   WRITE: / '学生信息:'.")
        print("   WRITE: / '姓名:', lv_name.")
        print("   WRITE: / '年龄:', lv_age.")
        print()
        
        # 练习部分
        print("🎯 **现在开始练习！**")
        print("=" * 50)
        print()
        
        # 先确保有一些变量可以使用
        if not self.variables:
            self.variables['lv_name'] = '张三'
            self.variables['lv_age'] = 25
            print("📊 为练习准备的变量：")
            print("   lv_name = '张三'")
            print("   lv_age = 25")
            print()
        
        exercises = [
            {
                "task": "输出文本 'ABAP 学习开始！'",
                "hint": "WRITE: 'ABAP 学习开始！'.",
                "expected_pattern": r"WRITE:\s*'[^']+'\s*\."
            },
            {
                "task": "在新行输出变量 lv_name 的值",
                "hint": "WRITE: / lv_name.",
                "expected_pattern": r"WRITE:\s*/\s+lv_name\s*\."
            },
            {
                "task": "输出 '年龄:' 和变量 lv_age",
                "hint": "WRITE: '年龄:', lv_age.",
                "expected_pattern": r"WRITE:\s*'年龄:',\s*lv_age\s*\."
            }
        ]
        
        for i, exercise in enumerate(exercises, 1):
            print(f"📝 **练习 {i}：**")
            print(f"   任务：{exercise['task']}")
            print(f"   提示：{exercise['hint']}")
            print()
            
            while True:
                user_input = input("请输入您的 ABAP 语句: ").strip()
                
                if not user_input:
                    print("❌ 输入不能为空，请重新输入！")
                    continue
                
                # 检查语法
                result = self._check_write_syntax(user_input, exercise['expected_pattern'])
                
                if result['valid']:
                    print("✅ 语法正确！")
                    print(f"📊 执行结果：{result['message']}")
                    print()
                    break
                else:
                    print(f"❌ 语法错误：{result['error']}")
                    print("💡 请参考提示重新输入")
                    print()
        
        print("🎉 恭喜！您已完成输出语句语法练习！")
        print()
        input("按 Enter 键继续...")
    
    def _check_write_syntax(self, statement: str, expected_pattern: str) -> Dict[str, Any]:
        """检查 WRITE 语句语法"""
        try:
            # 基本格式检查
            if not statement.endswith('.'):
                return {"valid": False, "error": "ABAP 语句必须以句点(.)结束"}
            
            if not statement.upper().startswith('WRITE'):
                return {"valid": False, "error": "输出语句必须以 WRITE 开头"}
            
            # 使用正则表达式检查语法
            if not re.match(expected_pattern, statement, re.IGNORECASE):
                return {"valid": False, "error": "语法格式不正确，请检查拼写和格式"}
            
            # 模拟执行输出
            output_parts = []
            
            # 解析 WRITE 语句内容
            content = statement[5:].strip()  # 去掉 "WRITE"
            if content.startswith(':'):
                content = content[1:].strip()  # 去掉冒号
            
            content = content[:-1]  # 去掉句点
            
            # 处理换行符
            if content.startswith('/'):
                output_parts.append("\\n")
                content = content[1:].strip()
            
            # 分割输出项
            items = [item.strip() for item in content.split(',')]
            
            for item in items:
                if item.startswith("'") and item.endswith("'"):
                    # 字符串字面量
                    output_parts.append(item[1:-1])
                elif item in self.variables:
                    # 变量
                    output_parts.append(str(self.variables[item]))
                elif item in self.constants:
                    # 常量
                    output_parts.append(str(self.constants[item]))
                else:
                    # 其他内容
                    output_parts.append(item)
            
            output_text = " ".join(output_parts)
            
            return {
                "valid": True,
                "message": f"输出内容：{output_text}"
            }
            
        except Exception as e:
            return {"valid": False, "error": f"语法分析错误：{str(e)}"}
    
    def run(self):
        """运行语法训练器"""
        self.show_welcome()
        
        while True:
            self.show_lesson_menu()
            choice = input("请选择课程 (0-10): ").strip()
            print()
            
            if choice == '1':
                self.lesson_data_declaration()
            elif choice == '2':
                self.lesson_constants_declaration()
            elif choice == '3':
                self.lesson_write_statement()
            elif choice == '4':
                print("🚧 第4课正在开发中...")
                input("按 Enter 键继续...")
            elif choice == '5':
                print("🚧 第5课正在开发中...")
                input("按 Enter 键继续...")
            elif choice == '6':
                print("🚧 第6课正在开发中...")
                input("按 Enter 键继续...")
            elif choice == '7':
                print("🚧 第7课正在开发中...")
                input("按 Enter 键继续...")
            elif choice == '8':
                print("🚧 第8课正在开发中...")
                input("按 Enter 键继续...")
            elif choice == '9':
                print("🚧 第9课正在开发中...")
                input("按 Enter 键继续...")
            elif choice == '10':
                print("🚧 第10课正在开发中...")
                input("按 Enter 键继续...")
            elif choice == '0':
                print("🎉 感谢使用 ABAP 语法练习训练器！")
                print("📚 继续练习，成为 ABAP 专家！")
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 0-10 之间的数字。")
                input("按 Enter 键继续...")

if __name__ == "__main__":
    trainer = ABAPSyntaxTrainer()
    trainer.run()
