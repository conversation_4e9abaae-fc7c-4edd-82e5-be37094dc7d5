#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大文件生成器
用于创建测试用的大型txt文件
"""

import os
import sys
from pathlib import Path


def generate_large_file(output_file, target_size_mb=25):
    """
    生成指定大小的测试文件
    
    参数:
        output_file (str): 输出文件路径
        target_size_mb (int): 目标文件大小（MB）
    """
    target_size_bytes = target_size_mb * 1024 * 1024
    current_size = 0
    line_count = 0
    
    # 创建一些不同长度的测试行模板
    line_templates = [
        "这是第{line_num}行测试数据，包含中等长度的内容用于测试文件分割功能。\n",
        "第{line_num}行：这是一个较短的测试行。\n",
        "这是第{line_num}行非常长的测试数据，包含大量文本内容用于快速增加文件大小。这是重复的内容，用来确保每一行都有足够的字符。abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*()\n",
        "测试行{line_num}：包含特殊字符和数字1234567890的测试数据。\n",
        "Line {line_num}: This is English test data mixed with Chinese characters. 中英文混合测试行。\n",
        "--------------------------------------------------------------------------------\n",  # 分隔线
        "第{line_num}行数据结束。\n"
    ]
    
    print(f"开始生成 {target_size_mb} MB 的测试文件: {output_file}")
    print(f"目标大小: {target_size_bytes:,} 字节")
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            while current_size < target_size_bytes:
                # 轮流使用不同的行模板
                template_index = line_count % len(line_templates)
                line_content = line_templates[template_index].format(line_num=line_count + 1)
                
                # 写入行内容
                f.write(line_content)
                current_size += len(line_content.encode('utf-8'))
                line_count += 1
                
                # 每10000行显示一次进度
                if line_count % 10000 == 0:
                    progress_mb = current_size / (1024 * 1024)
                    print(f"已生成 {line_count:,} 行, {progress_mb:.2f} MB")
                
                # 防止无限循环的安全检查
                if line_count > 1000000:  # 最多100万行
                    print("达到最大行数限制，停止生成")
                    break
        
        print(f"文件生成完成！")
        print(f"实际行数: {line_count:,}")
        print(f"实际大小: {current_size / (1024 * 1024):.2f} MB ({current_size:,} 字节)")
        
        return True
        
    except Exception as e:
        print(f"生成文件时发生错误: {e}")
        return False


def main():
    """主函数"""
    print("大文件生成器")
    print("=" * 50)
    
    # 默认参数
    output_file = "large_test_file.txt"
    target_size_mb = 25
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        output_file = sys.argv[1]
    
    if len(sys.argv) > 2:
        try:
            target_size_mb = int(sys.argv[2])
        except ValueError:
            print("错误：文件大小必须是数字")
            return
    
    print(f"输出文件: {output_file}")
    print(f"目标大小: {target_size_mb} MB")
    print("-" * 50)
    
    # 生成大文件
    success = generate_large_file(output_file, target_size_mb)
    
    if success:
        print("\n✅ 文件生成成功！")
    else:
        print("\n❌ 文件生成失败！")


if __name__ == "__main__":
    main()
