#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式 ABAP 学习模拟器
作者: ABAP 学习助手
描述: 交互式学习 ABAP 编程概念，支持用户输入和实时练习
"""

import datetime
import os
from typing import Any, Dict, List

class InteractiveABAPSimulator:
    """交互式 ABAP 语法模拟器类"""
    
    def __init__(self):
        """初始化模拟器"""
        self.variables = {}  # 存储变量
        self.constants = {}  # 存储常量
        self.sy_variables = self._init_system_variables()  # 系统变量
        self.output_lines = []  # 输出行
        
    def _init_system_variables(self) -> Dict[str, Any]:
        """初始化系统变量 (sy-*)"""
        return {
            'sy-datum': datetime.date.today().strftime('%Y%m%d'),  # 当前日期
            'sy-uzeit': datetime.datetime.now().strftime('%H%M%S'),  # 当前时间
            'sy-uname': os.getenv('USERNAME', 'ABAP_LEARNER'),  # 用户名
            'sy-index': 0,  # 循环索引
            'sy-tabix': 0,  # 表索引
            'sy-subrc': 0   # 返回码
        }
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("🎉" + "=" * 60 + "🎉")
        print("🚀          欢迎使用交互式 ABAP 学习模拟器！          🚀")
        print("🎉" + "=" * 60 + "🎉")
        print()
        print("📚 这个模拟器可以帮助您学习 ABAP 编程语言的基础概念")
        print("💡 您可以通过菜单选择不同的学习模块")
        print("✨ 每个模块都包含详细的解释和实际的代码演示")
        print()
    
    def show_menu(self):
        """显示主菜单"""
        print("📋" + "=" * 50 + "📋")
        print("🎯                ABAP 学习菜单                🎯")
        print("📋" + "=" * 50 + "📋")
        print()
        print("1️⃣  数据声明和变量 (DATA Declaration)")
        print("2️⃣  常量声明 (CONSTANTS)")
        print("3️⃣  条件判断 (IF-ELSE)")
        print("4️⃣  CASE 语句 (CASE Statement)")
        print("5️⃣  DO 循环 (DO Loop)")
        print("6️⃣  WHILE 循环 (WHILE Loop)")
        print("7️⃣  字符串处理 (String Processing)")
        print("8️⃣  数值运算 (Numeric Operations)")
        print("9️⃣  系统变量 (System Variables)")
        print("🔟  综合练习 (Comprehensive Exercise)")
        print("0️⃣  退出程序 (Exit)")
        print()
    
    def lesson_data_declaration(self):
        """第1课：数据声明"""
        print("📚" + "=" * 50 + "📚")
        print("📝              第1课：数据声明和变量              📝")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 在 ABAP 中，我们使用 DATA 语句来声明变量")
        print("📖 语法格式：DATA: 变量名 TYPE 数据类型 VALUE 初始值.")
        print()
        
        # 演示不同数据类型
        print("🔤 字符串类型 (STRING):")
        name = input("   请输入您的姓名: ")
        self.variables['lv_name'] = name
        print(f"   ✅ DATA: lv_name TYPE string VALUE '{name}'.")
        print()
        
        print("🔢 整数类型 (I):")
        try:
            age = int(input("   请输入您的年龄: "))
            self.variables['lv_age'] = age
            print(f"   ✅ DATA: lv_age TYPE i VALUE {age}.")
        except ValueError:
            print("   ❌ 输入无效，使用默认值 25")
            self.variables['lv_age'] = 25
        print()
        
        print("📅 日期类型 (D):")
        self.variables['lv_today'] = self.sy_variables['sy-datum']
        print(f"   ✅ DATA: lv_today TYPE d VALUE '{self.sy_variables['sy-datum']}'.")
        print()
        
        print("📊 您声明的变量:")
        for var_name, var_value in self.variables.items():
            print(f"   {var_name} = {var_value}")
        print()
        
        input("按 Enter 键继续...")
    
    def lesson_constants(self):
        """第2课：常量声明"""
        print("📚" + "=" * 50 + "📚")
        print("🔒              第2课：常量声明              🔒")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 常量是程序中不会改变的值")
        print("📖 语法格式：CONSTANTS: 常量名 TYPE 数据类型 VALUE 值.")
        print()
        
        # 声明一些常量
        self.constants['lc_max_score'] = 100
        self.constants['lc_company'] = 'SAP学习中心'
        self.constants['lc_pi'] = 3.14159
        
        print("🏢 公司常量:")
        print(f"   CONSTANTS: lc_company TYPE string VALUE '{self.constants['lc_company']}'.")
        print()
        
        print("🔢 数值常量:")
        print(f"   CONSTANTS: lc_max_score TYPE i VALUE {self.constants['lc_max_score']}.")
        print(f"   CONSTANTS: lc_pi TYPE f VALUE {self.constants['lc_pi']}.")
        print()
        
        print("📊 已声明的常量:")
        for const_name, const_value in self.constants.items():
            print(f"   {const_name} = {const_value}")
        print()
        
        input("按 Enter 键继续...")
    
    def lesson_if_statement(self):
        """第3课：条件判断"""
        print("📚" + "=" * 50 + "📚")
        print("🔍              第3课：条件判断              🔍")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 IF 语句用于根据条件执行不同的代码")
        print("📖 语法格式：IF 条件. ... ELSE. ... ENDIF.")
        print()
        
        # 获取用户输入的分数
        try:
            score = int(input("请输入您的考试分数 (0-100): "))
        except ValueError:
            score = 75
            print(f"输入无效，使用默认分数: {score}")
        
        print()
        print("🎯 分数评定程序:")
        print(f"   您的分数: {score}")
        print()
        
        # 条件判断逻辑
        print("📝 ABAP 代码逻辑:")
        print("   IF lv_score >= 90.")
        print("     WRITE: '优秀'.")
        print("   ELSEIF lv_score >= 80.")
        print("     WRITE: '良好'.")
        print("   ELSEIF lv_score >= 70.")
        print("     WRITE: '中等'.")
        print("   ELSEIF lv_score >= 60.")
        print("     WRITE: '及格'.")
        print("   ELSE.")
        print("     WRITE: '不及格'.")
        print("   ENDIF.")
        print()
        
        # 执行判断
        print("🏆 执行结果:")
        if score >= 90:
            print("   🌟 优秀！您的成绩非常棒！")
            grade = "A"
        elif score >= 80:
            print("   👍 良好！继续保持！")
            grade = "B"
        elif score >= 70:
            print("   📖 中等，还有提升空间！")
            grade = "C"
        elif score >= 60:
            print("   📝 及格，需要更加努力！")
            grade = "D"
        else:
            print("   📚 不及格，建议重新学习！")
            grade = "F"
        
        self.variables['lv_score'] = score
        self.variables['lv_grade'] = grade
        print()
        
        input("按 Enter 键继续...")
    
    def lesson_case_statement(self):
        """第4课：CASE 语句"""
        print("📚" + "=" * 50 + "📚")
        print("🎯              第4课：CASE 语句              🎯")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 CASE 语句用于多分支选择")
        print("📖 语法格式：CASE 变量. WHEN 值1. ... WHEN 值2. ... ENDCASE.")
        print()
        
        # 获取用户选择
        print("🎮 请选择您喜欢的编程语言:")
        print("   A - ABAP")
        print("   J - Java")
        print("   P - Python")
        print("   C - C++")
        
        choice = input("请输入您的选择 (A/J/P/C): ").upper()
        print()
        
        print("📝 ABAP 代码逻辑:")
        print("   CASE lv_choice.")
        print("     WHEN 'A'.")
        print("       WRITE: 'ABAP - SAP的企业级编程语言'.")
        print("     WHEN 'J'.")
        print("       WRITE: 'Java - 跨平台面向对象语言'.")
        print("     WHEN 'P'.")
        print("       WRITE: 'Python - 简洁优雅的脚本语言'.")
        print("     WHEN 'C'.")
        print("       WRITE: 'C++ - 高性能系统编程语言'.")
        print("     WHEN OTHERS.")
        print("       WRITE: '未知选择'.")
        print("   ENDCASE.")
        print()
        
        # 执行 CASE 逻辑
        print("🏆 执行结果:")
        if choice == 'A':
            print("   🏢 ABAP - SAP的企业级编程语言")
            print("   💼 专为企业资源规划(ERP)系统设计")
        elif choice == 'J':
            print("   ☕ Java - 跨平台面向对象语言")
            print("   🌐 一次编写，到处运行")
        elif choice == 'P':
            print("   🐍 Python - 简洁优雅的脚本语言")
            print("   🚀 人工智能和数据科学的首选")
        elif choice == 'C':
            print("   ⚡ C++ - 高性能系统编程语言")
            print("   🎮 游戏开发和系统编程的利器")
        else:
            print("   ❓ 未知选择，请选择 A、J、P 或 C")
        
        self.variables['lv_choice'] = choice
        print()
        
        input("按 Enter 键继续...")
    
    def lesson_do_loop(self):
        """第5课：DO 循环"""
        print("📚" + "=" * 50 + "📚")
        print("🔄              第5课：DO 循环              🔄")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 DO 循环用于重复执行代码指定次数")
        print("📖 语法格式：DO n TIMES. ... ENDDO.")
        print("🔢 sy-index 系统变量记录当前循环次数")
        print()
        
        # 获取循环次数
        try:
            times = int(input("请输入循环次数 (1-10): "))
            if times < 1 or times > 10:
                times = 5
                print(f"输入超出范围，使用默认值: {times}")
        except ValueError:
            times = 5
            print(f"输入无效，使用默认值: {times}")
        
        print()
        print("📝 ABAP 代码逻辑:")
        print(f"   DO {times} TIMES.")
        print("     WRITE: '循环第', sy-index, '次'.")
        print("   ENDDO.")
        print()
        
        # 执行循环
        print("🏆 执行结果:")
        total = 0
        for i in range(1, times + 1):
            self.sy_variables['sy-index'] = i
            print(f"   🔄 循环第 {i} 次 (sy-index = {i})")
            total += i
        
        print()
        print(f"📊 循环统计:")
        print(f"   总循环次数: {times}")
        print(f"   累计求和: 1+2+...+{times} = {total}")
        print()
        
        input("按 Enter 键继续...")
    
    def lesson_while_loop(self):
        """第6课：WHILE 循环"""
        print("📚" + "=" * 50 + "📚")
        print("⏰              第6课：WHILE 循环              ⏰")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 WHILE 循环根据条件重复执行代码")
        print("📖 语法格式：WHILE 条件. ... ENDWHILE.")
        print()
        
        # 获取目标数值
        try:
            target = int(input("请输入目标数值 (10-100): "))
            if target < 10 or target > 100:
                target = 50
                print(f"输入超出范围，使用默认值: {target}")
        except ValueError:
            target = 50
            print(f"输入无效，使用默认值: {target}")
        
        print()
        print("📝 ABAP 代码逻辑 (计算2的幂次):")
        print("   DATA: lv_result TYPE i VALUE 1.")
        print("   DATA: lv_power TYPE i VALUE 0.")
        print(f"   WHILE lv_result < {target}.")
        print("     lv_result = lv_result * 2.")
        print("     lv_power = lv_power + 1.")
        print("   ENDWHILE.")
        print()
        
        # 执行 WHILE 循环
        print("🏆 执行结果:")
        result = 1
        power = 0
        
        print(f"   🎯 寻找第一个大于等于 {target} 的2的幂次:")
        while result < target:
            print(f"   🔄 2^{power} = {result} < {target}")
            result *= 2
            power += 1
        
        print(f"   ✅ 找到了！2^{power} = {result} >= {target}")
        print()
        print(f"📊 结果统计:")
        print(f"   目标数值: {target}")
        print(f"   幂次: {power}")
        print(f"   结果: 2^{power} = {result}")
        print()
        
        input("按 Enter 键继续...")
    
    def lesson_string_processing(self):
        """第7课：字符串处理"""
        print("📚" + "=" * 50 + "📚")
        print("📝              第7课：字符串处理              📝")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 ABAP 提供了丰富的字符串处理功能")
        print("📖 包括连接、分割、查找、替换等操作")
        print()
        
        # 获取用户输入
        first_name = input("请输入您的名字: ")
        last_name = input("请输入您的姓氏: ")
        
        print()
        print("📝 ABAP 字符串操作演示:")
        print()
        
        # 字符串连接
        full_name = f"{last_name}{first_name}"
        print("🔗 字符串连接:")
        print(f"   DATA(lv_full_name) = |{ last_name }{ first_name }|.")
        print(f"   结果: '{full_name}'")
        print()
        
        # 字符串长度
        name_length = len(full_name)
        print("📏 字符串长度:")
        print("   DATA(lv_length) = strlen( lv_full_name ).")
        print(f"   结果: {name_length} 个字符")
        print()
        
        # 字符串转换
        upper_name = full_name.upper()
        lower_name = full_name.lower()
        print("🔄 大小写转换:")
        print("   DATA(lv_upper) = to_upper( lv_full_name ).")
        print(f"   大写: '{upper_name}'")
        print("   DATA(lv_lower) = to_lower( lv_full_name ).")
        print(f"   小写: '{lower_name}'")
        print()
        
        # 字符串查找
        if '张' in full_name:
            pos = full_name.find('张') + 1  # ABAP 索引从1开始
            print("🔍 字符串查找:")
            print("   IF contains( val = lv_full_name sub = '张' ).")
            print(f"   找到 '张' 在位置: {pos}")
        else:
            print("🔍 字符串查找:")
            print("   未找到 '张' 字符")
        print()
        
        # 字符串模板
        greeting = f"欢迎 {full_name} 学习 ABAP 编程！"
        print("📋 字符串模板:")
        print("   DATA(lv_greeting) = |欢迎 { lv_full_name } 学习 ABAP 编程！|.")
        print(f"   结果: '{greeting}'")
        print()
        
        input("按 Enter 键继续...")
    
    def lesson_numeric_operations(self):
        """第8课：数值运算"""
        print("📚" + "=" * 50 + "📚")
        print("🧮              第8课：数值运算              🧮")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 ABAP 支持各种数学运算和函数")
        print("📖 包括基本运算、数学函数、类型转换等")
        print()
        
        # 获取两个数值
        try:
            num1 = float(input("请输入第一个数值: "))
            num2 = float(input("请输入第二个数值: "))
        except ValueError:
            num1, num2 = 10.5, 3.2
            print(f"输入无效，使用默认值: {num1}, {num2}")
        
        print()
        print("📝 ABAP 数值运算演示:")
        print()
        
        # 基本运算
        print("➕ 基本运算:")
        print(f"   {num1} + {num2} = {num1 + num2}")
        print(f"   {num1} - {num2} = {num1 - num2}")
        print(f"   {num1} * {num2} = {num1 * num2}")
        if num2 != 0:
            print(f"   {num1} / {num2} = {num1 / num2:.2f}")
            print(f"   {num1} MOD {num2} = {num1 % num2:.2f}")
        print()
        
        # 数学函数
        import math
        print("📐 数学函数:")
        print(f"   abs( {num1} ) = {abs(num1)}")
        print(f"   sqrt( {abs(num1)} ) = {math.sqrt(abs(num1)):.2f}")
        print(f"   ceil( {num1} ) = {math.ceil(num1)}")
        print(f"   floor( {num1} ) = {math.floor(num1)}")
        print()
        
        # 类型转换
        print("🔄 类型转换:")
        int_num1 = int(num1)
        print(f"   conv i( {num1} ) = {int_num1}")
        print(f"   conv string( {num2} ) = '{str(num2)}'")
        print()
        
        # 条件运算
        max_num = max(num1, num2)
        min_num = min(num1, num2)
        print("🔍 条件运算:")
        print(f"   COND #( WHEN {num1} > {num2} THEN {num1} ELSE {num2} ) = {max_num}")
        print(f"   最大值: {max_num}")
        print(f"   最小值: {min_num}")
        print()
        
        input("按 Enter 键继续...")
    
    def lesson_system_variables(self):
        """第9课：系统变量"""
        print("📚" + "=" * 50 + "📚")
        print("🖥️              第9课：系统变量              🖥️")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 ABAP 提供了许多有用的系统变量 (sy-*)")
        print("📖 这些变量包含系统信息和程序执行状态")
        print()
        
        # 更新系统变量
        self.sy_variables['sy-datum'] = datetime.date.today().strftime('%Y%m%d')
        self.sy_variables['sy-uzeit'] = datetime.datetime.now().strftime('%H%M%S')
        
        print("📊 常用系统变量:")
        print()
        
        print("📅 日期时间相关:")
        print(f"   sy-datum (系统日期): {self.sy_variables['sy-datum']}")
        print(f"   sy-uzeit (系统时间): {self.sy_variables['sy-uzeit']}")
        print()
        
        print("👤 用户相关:")
        print(f"   sy-uname (用户名): {self.sy_variables['sy-uname']}")
        print()
        
        print("🔄 循环相关:")
        print(f"   sy-index (循环索引): {self.sy_variables['sy-index']}")
        print(f"   sy-tabix (表索引): {self.sy_variables['sy-tabix']}")
        print()
        
        print("✅ 状态相关:")
        print(f"   sy-subrc (返回码): {self.sy_variables['sy-subrc']}")
        print("   (0 = 成功, 非0 = 失败)")
        print()
        
        # 演示系统变量在循环中的使用
        print("🔄 系统变量在循环中的应用:")
        print("   DO 3 TIMES.")
        print("     WRITE: '第', sy-index, '次循环'.")
        print("   ENDDO.")
        print()
        print("   执行结果:")
        for i in range(1, 4):
            self.sy_variables['sy-index'] = i
            print(f"     第 {i} 次循环 (sy-index = {i})")
        print()
        
        input("按 Enter 键继续...")
    
    def comprehensive_exercise(self):
        """第10课：综合练习"""
        print("📚" + "=" * 50 + "📚")
        print("🎯              第10课：综合练习              🎯")
        print("📚" + "=" * 50 + "📚")
        print()
        
        print("💡 现在让我们综合运用所学的 ABAP 概念")
        print("🎮 我们将创建一个简单的学生成绩管理程序")
        print()
        
        # 学生信息收集
        print("📋 学生信息录入:")
        student_name = input("学生姓名: ")
        try:
            student_age = int(input("学生年龄: "))
        except ValueError:
            student_age = 20
            print(f"年龄输入无效，使用默认值: {student_age}")
        
        # 成绩录入
        print("\n📊 成绩录入:")
        subjects = ['数学', '英语', '计算机']
        scores = {}
        
        for subject in subjects:
            try:
                score = float(input(f"{subject}成绩 (0-100): "))
                if 0 <= score <= 100:
                    scores[subject] = score
                else:
                    scores[subject] = 85.0
                    print(f"成绩超出范围，使用默认值: 85")
            except ValueError:
                scores[subject] = 85.0
                print(f"输入无效，使用默认值: 85")
        
        # 计算平均分
        total_score = sum(scores.values())
        average_score = total_score / len(scores)
        
        # 等级评定
        if average_score >= 90:
            grade = 'A'
            level = '优秀'
        elif average_score >= 80:
            grade = 'B'
            level = '良好'
        elif average_score >= 70:
            grade = 'C'
            level = '中等'
        elif average_score >= 60:
            grade = 'D'
            level = '及格'
        else:
            grade = 'F'
            level = '不及格'
        
        # 生成报告
        print("\n" + "🎓" + "=" * 50 + "🎓")
        print("📋                学生成绩报告                📋")
        print("🎓" + "=" * 50 + "🎓")
        print()
        
        print(f"👤 学生姓名: {student_name}")
        print(f"🎂 学生年龄: {student_age}")
        print(f"📅 报告日期: {self.sy_variables['sy-datum']}")
        print()
        
        print("📊 各科成绩:")
        for subject, score in scores.items():
            print(f"   {subject}: {score:.1f}")
        print()
        
        print("📈 统计信息:")
        print(f"   总分: {total_score:.1f}")
        print(f"   平均分: {average_score:.1f}")
        print(f"   等级: {grade} ({level})")
        print()
        
        # 建议
        print("💡 学习建议:")
        if average_score >= 90:
            print("   🌟 成绩优秀！继续保持，可以挑战更高难度的课程。")
        elif average_score >= 80:
            print("   👍 成绩良好！稍加努力就能达到优秀水平。")
        elif average_score >= 70:
            print("   📖 成绩中等，建议加强薄弱科目的学习。")
        elif average_score >= 60:
            print("   💪 成绩及格，需要更多的练习和复习。")
        else:
            print("   📚 成绩不理想，建议寻求老师或同学的帮助。")
        
        print()
        print("🎉 综合练习完成！您已经掌握了 ABAP 的基础概念！")
        print()
        
        input("按 Enter 键继续...")
    
    def run(self):
        """运行交互式模拟器"""
        self.show_welcome()
        
        while True:
            self.show_menu()
            choice = input("请选择学习模块 (0-10): ").strip()
            print()
            
            if choice == '1':
                self.lesson_data_declaration()
            elif choice == '2':
                self.lesson_constants()
            elif choice == '3':
                self.lesson_if_statement()
            elif choice == '4':
                self.lesson_case_statement()
            elif choice == '5':
                self.lesson_do_loop()
            elif choice == '6':
                self.lesson_while_loop()
            elif choice == '7':
                self.lesson_string_processing()
            elif choice == '8':
                self.lesson_numeric_operations()
            elif choice == '9':
                self.lesson_system_variables()
            elif choice == '10':
                self.comprehensive_exercise()
            elif choice == '0':
                print("🎉 感谢使用 ABAP 学习模拟器！")
                print("📚 继续努力学习，成为 ABAP 专家！")
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 0-10 之间的数字。")
                input("按 Enter 键继续...")

if __name__ == "__main__":
    simulator = InteractiveABAPSimulator()
    simulator.run()
