#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本文件分割工具
功能：将指定目录下的.txt文件按20MB大小进行分割，保持行的完整性
作者：Cline
日期：2025-07-28
"""

import os
import sys
from pathlib import Path


def split_txt_file(file_path, max_size_mb=20):
    """
    将单个txt文件按指定大小分割
    
    参数:
        file_path (str): 要分割的文件路径
        max_size_mb (int): 最大文件大小（MB），默认20MB
    
    返回:
        int: 分割后的文件数量
    """
    # 将MB转换为字节
    max_size_bytes = max_size_mb * 1024 * 1024
    
    # 获取文件路径信息
    file_path = Path(file_path)
    file_dir = file_path.parent
    file_name = file_path.stem
    file_extension = file_path.suffix
    
    # 检查文件是否存在
    if not file_path.exists():
        print(f"错误：文件 {file_path} 不存在")
        return 0
    
    # 检查是否为txt文件
    if file_extension.lower() != '.txt':
        print(f"错误：只支持.txt文件，当前文件为 {file_extension} 格式")
        return 0
    
    # 获取文件大小
    file_size = file_path.stat().st_size
    print(f"文件大小: {file_size / (1024*1024):.2f} MB")
    
    # 如果文件小于等于最大大小，不需要分割
    if file_size <= max_size_bytes:
        print("文件大小小于等于20MB，无需分割")
        return 1
    
    # 计算需要分割的文件数量
    expected_files = (file_size // max_size_bytes) + 1
    print(f"预计分割为 {expected_files} 个文件")
    
    # 开始分割文件
    file_count = 0
    current_size = 0
    output_file = None
    line_count = 0
    
    try:
        # 以二进制模式读取文件，避免编码问题
        with open(file_path, 'rb') as input_file:
            while True:
                # 准备创建新的输出文件
                file_count += 1
                output_path = file_dir / f"{file_name}_part{file_count:03d}{file_extension}"
                
                # 先检查是否还有数据可读
                # 保存当前位置
                current_pos = input_file.tell()
                test_line = input_file.readline()
                
                # 如果没有更多数据，跳出循环
                if not test_line:
                    file_count -= 1  # 不需要创建新文件
                    break
                
                # 如果还有数据，回退到原来位置
                input_file.seek(current_pos)
                
                print(f"创建分割文件: {output_path}")
                
                with open(output_path, 'wb') as output_file:
                    current_size = 0
                    lines_in_this_file = 0  # 记录当前文件的行数
                    
                    # 逐行读取并写入，确保行的完整性
                    while True:
                        # 读取一行
                        line = input_file.readline()
                        
                        # 如果读取到文件末尾，跳出循环
                        if not line:
                            break
                        
                        # 计算当前行的大小
                        line_size = len(line)
                        
                        # 检查添加这行后是否会超过大小限制
                        if current_size + line_size > max_size_bytes and current_size > 0:
                            # 如果超过限制且不是空文件，则将这行留到下一个文件
                            # 需要将文件指针回退一行
                            input_file.seek(-line_size, 1)
                            break
                        
                        # 写入当前行到输出文件
                        output_file.write(line)
                        current_size += line_size
                        line_count += 1
                        lines_in_this_file += 1
                        
                        # 显示进度
                        if line_count % 10000 == 0:
                            print(f"已处理 {line_count} 行")
                    
                    # 如果当前输出文件为空（没有写入任何数据），删除它
                    if current_size == 0:
                        output_file.close()
                        os.remove(output_path)
                        file_count -= 1
                        break
                
                print(f"文件 {output_path} 创建完成，大小: {current_size / (1024*1024):.2f} MB")
                
                # 如果没有写入任何行，说明已经处理完所有数据
                if lines_in_this_file == 0:
                    # 删除空文件
                    if output_path.exists():
                        os.remove(output_path)
                    file_count -= 1
                    break
        
        # 删除原始文件的选项（可选）
        # print(f"是否删除原始文件 {file_path}? (y/N): ", end="")
        # if input().lower() == 'y':
        #     os.remove(file_path)
        #     print(f"原始文件 {file_path} 已删除")
        
        print(f"文件分割完成！共创建 {file_count} 个文件")
        return file_count
        
    except Exception as e:
        print(f"分割文件时发生错误: {e}")
        return 0


def split_txt_files_in_directory(directory_path, max_size_mb=20):
    """
    分割指定目录下的所有txt文件
    
    参数:
        directory_path (str): 目录路径
        max_size_mb (int): 最大文件大小（MB），默认20MB
    """
    directory_path = Path(directory_path)
    
    # 检查目录是否存在
    if not directory_path.exists():
        print(f"错误：目录 {directory_path} 不存在")
        return
    
    if not directory_path.is_dir():
        print(f"错误：{directory_path} 不是一个目录")
        return
    
    # 获取所有txt文件
    txt_files = list(directory_path.glob("*.txt"))
    
    if not txt_files:
        print(f"目录 {directory_path} 中没有找到.txt文件")
        return
    
    print(f"找到 {len(txt_files)} 个txt文件")
    
    # 处理每个txt文件
    for txt_file in txt_files:
        print(f"\n正在处理文件: {txt_file}")
        print("-" * 50)
        split_txt_file(txt_file, max_size_mb)


def main():
    """
    主函数 - 程序入口点
    """
    print("文本文件分割工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python split_txt_files.py <文件路径>           # 分割单个文件")
        print("  python split_txt_files.py <目录路径> -d        # 分割目录下所有txt文件")
        print("\n示例:")
        print("  python split_txt_files.py example.txt")
        print("  python split_txt_files.py ./txt_files -d")
        return
    
    # 获取第一个参数（文件路径或目录路径）
    path = sys.argv[1]
    
    # 检查是否有 -d 参数（表示处理目录）
    process_directory = len(sys.argv) > 2 and sys.argv[2] == '-d'
    
    if process_directory:
        # 处理目录下的所有txt文件
        print(f"开始分割目录 {path} 下的所有txt文件...")
        split_txt_files_in_directory(path)
    else:
        # 处理单个文件
        print(f"开始分割文件 {path}...")
        split_txt_file(path)


# 程序入口点
if __name__ == "__main__":
    main()
